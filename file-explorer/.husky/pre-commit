#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running pre-commit validation..."

# Validate model metadata
echo "📋 Validating model metadata..."
npm run validate:models

if [ $? -ne 0 ]; then
  echo "❌ Model metadata validation failed!"
  echo "🔧 Please fix all violations before committing."
  exit 1
fi

echo "✅ Model metadata validation passed!"

# Optional: Run linting
echo "🧹 Running linter..."
npm run lint

if [ $? -ne 0 ]; then
  echo "⚠️  Linting issues found, but allowing commit to proceed."
  echo "💡 Please fix linting issues when possible."
fi

echo "🎉 Pre-commit checks completed!"
