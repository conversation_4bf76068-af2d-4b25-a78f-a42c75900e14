#!/usr/bin/env node
// scripts/validateModelMetadata.js
// CI-Safe Auto-Validation Script for Model Metadata & Compliance Enforcement (JavaScript version)

const fs = require('fs');
const path = require('path');

// Configuration
const FORBIDDEN_KEYWORDS = [
  'test', 'mock', 'placeholder', 'demo', 'example', 'fake', 'scaffold', 'dummy',
  'sample', 'template', 'boilerplate', 'stub', 'temporary', 'temp'
];

const FORBIDDEN_MODEL_PATTERNS = [
  /test-model-\d+/i,
  /mock-\w+/i,
  /placeholder-\w+/i,
  /demo-\w+/i,
  /example-\w+/i,
  /fake-\w+/i,
  /dummy-\w+/i
];

class MetadataValidator {
  constructor() {
    this.results = [];
    this.totalFiles = 0;
    this.totalViolations = 0;
    this.totalWarnings = 0;
  }

  /**
   * Main validation entry point
   */
  async validate() {
    console.log('🔍 Starting Model Metadata Validation...\n');

    const baseDir = path.resolve(__dirname, '..');
    const metadataFiles = this.findMetadataFiles(baseDir);

    if (metadataFiles.length === 0) {
      console.log('⚠️  No metadata files found to validate');
      return true;
    }

    console.log(`📁 Found ${metadataFiles.length} metadata files to validate:`);
    metadataFiles.forEach(file => console.log(`   - ${path.relative(baseDir, file)}`));
    console.log('');

    for (const file of metadataFiles) {
      await this.validateFile(file);
    }

    return this.generateReport();
  }

  /**
   * Find all model metadata files
   */
  findMetadataFiles(baseDir) {
    const files = [];
    const agentsDir = path.join(baseDir, 'components', 'agents');

    if (!fs.existsSync(agentsDir)) {
      console.warn(`⚠️  Agents directory not found: ${agentsDir}`);
      return files;
    }

    const agentFiles = fs.readdirSync(agentsDir);

    for (const file of agentFiles) {
      if (file.endsWith('-models.ts') || file.includes('model') || file.includes('metadata')) {
        const fullPath = path.join(agentsDir, file);
        if (fs.statSync(fullPath).isFile()) {
          files.push(fullPath);
        }
      }
    }

    return files;
  }

  /**
   * Validate a single metadata file
   */
  async validateFile(filePath) {
    this.totalFiles++;
    const result = {
      file: path.relative(path.resolve(__dirname, '..'), filePath),
      violations: [],
      warnings: []
    };

    try {
      const content = fs.readFileSync(filePath, 'utf8');

      // Check for forbidden keywords
      this.checkForbiddenKeywords(content, result);

      // Check for forbidden model patterns
      this.checkForbiddenModelPatterns(content, result);

      // Check for placeholder pricing
      this.checkPlaceholderPricing(content, result);

      // Check for missing required fields
      this.checkRequiredFields(content, result);

      // Check for suspicious pricing values
      this.checkSuspiciousPricing(content, result);

      // Check for proper metadata structure
      this.checkMetadataStructure(content, result);

    } catch (error) {
      result.violations.push(`❌ Failed to read file: ${error.message}`);
    }

    if (result.violations.length > 0 || result.warnings.length > 0) {
      this.results.push(result);
    }

    this.totalViolations += result.violations.length;
    this.totalWarnings += result.warnings.length;
  }

  /**
   * Check for forbidden keywords in content
   */
  checkForbiddenKeywords(content, result) {
    const lowerContent = content.toLowerCase();

    for (const keyword of FORBIDDEN_KEYWORDS) {
      if (lowerContent.includes(keyword)) {
        // Check if it's in a comment or safe context
        const lines = content.split('\n');
        const matchingLines = lines
          .map((line, index) => ({ line: line.toLowerCase(), number: index + 1, original: line }))
          .filter(({ line }) => line.includes(keyword));

        for (const { line, number, original } of matchingLines) {
          // Skip if it's in a comment explaining what NOT to do
          if (line.includes('//') && (line.includes('forbidden') || line.includes('not') || line.includes('avoid'))) {
            continue;
          }

          // Skip if it's in a string explaining validation
          if (line.includes('forbidden_keywords') || line.includes('validation')) {
            continue;
          }

          // Skip legitimate uses of keywords
          if (this.isLegitimateKeywordUse(keyword, line, original)) {
            continue;
          }

          result.violations.push(`❌ Forbidden keyword "${keyword}" found at line ${number}: ${original.trim()}`);
        }
      }
    }
  }

  /**
   * Check if keyword use is legitimate (not a violation)
   */
  isLegitimateKeywordUse(keyword, lowerLine, originalLine) {
    switch (keyword) {
      case 'test':
        // Allow "latest", "fastest", "contest", etc.
        return lowerLine.includes('latest') ||
               lowerLine.includes('fastest') ||
               lowerLine.includes('contest') ||
               lowerLine.includes('greatest') ||
               originalLine.includes('Latest') ||
               originalLine.includes('Fastest');

      case 'placeholder':
        // Allow placeholder as a prop name or parameter
        return lowerLine.includes('placeholder?:') ||
               lowerLine.includes('placeholder =') ||
               lowerLine.includes('placeholder,') ||
               lowerLine.includes('placeholder}') ||
               lowerLine.includes('{placeholder}') ||
               originalLine.includes('placeholder="') ||
               originalLine.includes('placeholder={');

      case 'example':
        // Allow "example" in comments or documentation
        return lowerLine.includes('//') ||
               lowerLine.includes('/*') ||
               lowerLine.includes('*') ||
               lowerLine.includes('description:') ||
               originalLine.includes('(e.g.,');

      case 'demo':
        // Allow "demo" in legitimate contexts
        return lowerLine.includes('//') ||
               lowerLine.includes('/*') ||
               lowerLine.includes('description:');

      default:
        return false;
    }
  }

  /**
   * Check for forbidden model ID patterns
   */
  checkForbiddenModelPatterns(content, result) {
    for (const pattern of FORBIDDEN_MODEL_PATTERNS) {
      const matches = content.match(pattern);
      if (matches) {
        result.violations.push(`❌ Forbidden model pattern found: ${matches[0]}`);
      }
    }
  }

  /**
   * Check for placeholder pricing values
   */
  checkPlaceholderPricing(content, result) {
    // Only check for exact zero pricing that indicates placeholders
    const placeholderPatterns = [
      /input:\s*0\.0+[^0-9]/g,  // input: 0.0, 0.00, etc. (but not 0.001)
      /output:\s*0\.0+[^0-9]/g, // output: 0.0, 0.00, etc. (but not 0.001)
      /input:\s*0[^.0-9]/g,     // input: 0 (without decimal, not part of larger number)
      /output:\s*0[^.0-9]/g     // output: 0 (without decimal, not part of larger number)
    ];

    for (const pattern of placeholderPatterns) {
      const matches = content.match(pattern);
      if (matches) {
        // Double-check that this isn't a legitimate zero pricing (like free models)
        for (const match of matches) {
          const context = this.getLineContext(content, match);
          if (!this.isLegitimateZeroPricing(context)) {
            result.violations.push(`❌ Placeholder pricing detected: ${match.trim()}`);
          }
        }
      }
    }

    // Check for obviously fake pricing patterns
    const fakePricingPatterns = [
      /input:\s*999/g,
      /output:\s*999/g,
      /input:\s*0\.123456/g,
      /output:\s*0\.123456/g,
      /input:\s*1\.0+$/g,  // Exactly 1.0 (suspicious)
      /output:\s*1\.0+$/g  // Exactly 1.0 (suspicious)
    ];

    for (const pattern of fakePricingPatterns) {
      const matches = content.match(pattern);
      if (matches) {
        result.violations.push(`❌ Suspicious pricing pattern detected: ${matches[0]}`);
      }
    }
  }

  /**
   * Get context around a match for better analysis
   */
  getLineContext(content, match) {
    const index = content.indexOf(match);
    const lines = content.split('\n');
    let currentPos = 0;

    for (const line of lines) {
      if (currentPos + line.length >= index) {
        return line;
      }
      currentPos += line.length + 1; // +1 for newline
    }
    return '';
  }

  /**
   * Check if zero pricing is legitimate (e.g., free models)
   */
  isLegitimateZeroPricing(context) {
    const lowerContext = context.toLowerCase();
    return lowerContext.includes('free') ||
           lowerContext.includes('no cost') ||
           lowerContext.includes('complimentary') ||
           lowerContext.includes('// free model') ||
           lowerContext.includes('beta') ||
           lowerContext.includes('preview');
  }

  /**
   * Check for required fields in metadata
   */
  checkRequiredFields(content, result) {
    // Check if metadata objects have required fields
    const metadataBlocks = content.match(/{\s*id:\s*['"][^'"]+['"][^}]+}/g);

    if (metadataBlocks) {
      for (const block of metadataBlocks) {
        if (!block.includes('label:')) {
          result.violations.push(`❌ Missing required field 'label' in metadata block`);
        }

        if (!block.includes('description:') && !block.includes('// No description')) {
          result.warnings.push(`⚠️  Missing 'description' field in metadata block`);
        }
      }
    }
  }

  /**
   * Check for suspicious pricing values
   */
  checkSuspiciousPricing(content, result) {
    // Check for pricing that seems too high or too low
    const pricingMatches = content.match(/(?:input|output):\s*([\d.]+)/g);

    if (pricingMatches) {
      for (const match of pricingMatches) {
        const value = parseFloat(match.split(':')[1].trim());

        if (value > 1.0) {
          result.warnings.push(`⚠️  Unusually high pricing detected: ${match} (>1.0)`);
        }

        if (value > 0 && value < 0.00001) {
          result.warnings.push(`⚠️  Unusually low pricing detected: ${match} (<0.00001)`);
        }
      }
    }
  }

  /**
   * Check metadata structure and exports
   */
  checkMetadataStructure(content, result) {
    // Check for proper TypeScript interface
    if (!content.includes('interface') && !content.includes('type')) {
      result.warnings.push(`⚠️  No TypeScript interface/type definitions found`);
    }

    // Check for proper exports
    if (!content.includes('export')) {
      result.violations.push(`❌ No exports found - metadata not accessible`);
    }

    // Check for metadata constant
    if (!content.includes('_METADATA') && !content.includes('_MODELS')) {
      result.warnings.push(`⚠️  No metadata constant found (expected *_METADATA or *_MODELS)`);
    }
  }

  /**
   * Generate validation report
   */
  generateReport() {
    console.log('📊 Validation Report');
    console.log('='.repeat(50));
    console.log(`📁 Files scanned: ${this.totalFiles}`);
    console.log(`❌ Total violations: ${this.totalViolations}`);
    console.log(`⚠️  Total warnings: ${this.totalWarnings}`);
    console.log('');

    if (this.results.length === 0) {
      console.log('✅ All metadata files passed validation!');
      console.log('🎉 No compliance violations found.');
      return true;
    }

    console.log('🚫 Issues found in the following files:');
    console.log('');

    for (const result of this.results) {
      console.log(`📄 ${result.file}`);

      if (result.violations.length > 0) {
        console.log('  Violations:');
        result.violations.forEach(violation => console.log(`    ${violation}`));
      }

      if (result.warnings.length > 0) {
        console.log('  Warnings:');
        result.warnings.forEach(warning => console.log(`    ${warning}`));
      }

      console.log('');
    }

    if (this.totalViolations > 0) {
      console.log('💥 VALIDATION FAILED');
      console.log('🔧 Please fix all violations before proceeding.');
      return false;
    } else {
      console.log('✅ VALIDATION PASSED');
      console.log('⚠️  Please review warnings when possible.');
      return true;
    }
  }
}

// Main execution
async function main() {
  const validator = new MetadataValidator();
  const success = await validator.validate();

  process.exit(success ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Validation script failed:', error);
    process.exit(1);
  });
}

module.exports = { MetadataValidator };
