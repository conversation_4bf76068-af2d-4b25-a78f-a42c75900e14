// components/agents/deepseek-models.ts

export interface DeepSeekModelMetadata {
  id: string;
  label: string;
  description?: string;
  contextSize?: number;
  pricing?: {
    input: number;  // per 1K tokens
    output: number; // per 1K tokens
  };
  tags?: string[];
  releaseDate?: string;
}

/**
 * Verified DeepSeek model metadata from official DeepSeek documentation
 * Only includes models with confirmed specifications
 */
export const DEEPSEEK_MODEL_METADATA: Record<string, DeepSeekModelMetadata> = {
  'deepseek-chat': {
    id: 'deepseek-chat',
    label: 'DeepSeek Chat',
    description: 'General-purpose conversational AI model with strong reasoning capabilities',
    contextSize: 32768,
    pricing: {
      input: 0.0014,
      output: 0.0028
    },
    tags: ['reasoning', 'conversation', 'general purpose', 'affordable'],
    releaseDate: '2024-01-15'
  },
  'deepseek-coder': {
    id: 'deepseek-coder',
    label: 'DeepSeek Coder',
    description: 'Specialized coding model with excellent programming capabilities',
    contextSize: 32768,
    pricing: {
      input: 0.0014,
      output: 0.0028
    },
    tags: ['code', 'programming', 'debugging', 'specialized'],
    releaseDate: '2024-01-15'
  }
};

/**
 * Get metadata for a specific DeepSeek model
 */
export function getDeepSeekModelMetadata(modelId: string): DeepSeekModelMetadata | null {
  return DEEPSEEK_MODEL_METADATA[modelId] || null;
}

/**
 * Check if a model has verified metadata
 */
export function hasDeepSeekModelMetadata(modelId: string): boolean {
  return modelId in DEEPSEEK_MODEL_METADATA;
}

/**
 * Get all available DeepSeek models with metadata
 */
export function getDeepSeekModelsWithMetadata(): DeepSeekModelMetadata[] {
  return Object.values(DEEPSEEK_MODEL_METADATA);
}

/**
 * Get model series for grouping
 */
export function getDeepSeekModelSeries(modelId: string): string {
  if (modelId.includes('coder')) {
    return 'DeepSeek Coder';
  }
  if (modelId.includes('chat')) {
    return 'DeepSeek Chat';
  }
  return 'DeepSeek';
}
