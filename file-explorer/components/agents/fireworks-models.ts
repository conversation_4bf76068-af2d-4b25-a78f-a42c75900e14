// components/agents/fireworks-models.ts

export interface FireworksModelMetadata {
  id: string;
  label: string;
  description?: string;
  contextSize?: number;
  pricing?: {
    input: number;  // per 1K tokens
    output: number; // per 1K tokens
  };
  tags?: string[];
  provider?: string; // Original model provider
}

/**
 * Verified Fireworks AI model metadata from official Fireworks documentation
 * Only includes models with confirmed specifications
 */
export const FIREWORKS_MODEL_METADATA: Record<string, FireworksModelMetadata> = {
  'llama-3.1-70b': {
    id: 'accounts/fireworks/models/llama-v3p1-70b-instruct',
    label: 'Llama 3.1 70B Instruct',
    description: 'Meta\'s flagship open-source model optimized for fast inference on Fireworks',
    contextSize: 131072,
    pricing: {
      input: 0.0009,
      output: 0.0009
    },
    tags: ['open-weight', 'reasoning', 'code', 'long-context', 'fast'],
    provider: 'Meta'
  },
  'mixtral-8x7b': {
    id: 'accounts/fireworks/models/mixtral-8x7b-instruct',
    label: 'Mixtral 8x7B Instruct',
    description: 'Mistral\'s mixture of experts model with excellent performance-to-cost ratio',
    contextSize: 32768,
    pricing: {
      input: 0.0009,
      output: 0.0009
    },
    tags: ['open-weight', 'mixture-of-experts', 'reasoning', 'fast'],
    provider: 'Mistral AI'
  },
  'qwen-72b': {
    id: 'accounts/fireworks/models/qwen2-72b-instruct',
    label: 'Qwen2 72B Instruct',
    description: 'Alibaba\'s large language model with strong multilingual capabilities',
    contextSize: 32768,
    pricing: {
      input: 0.0009,
      output: 0.0009
    },
    tags: ['multilingual', 'reasoning', 'open-weight', 'large'],
    provider: 'Alibaba'
  }
};

/**
 * Get metadata for a specific Fireworks model
 */
export function getFireworksModelMetadata(modelId: string): FireworksModelMetadata | null {
  return FIREWORKS_MODEL_METADATA[modelId] || null;
}

/**
 * Check if a model has verified metadata
 */
export function hasFireworksModelMetadata(modelId: string): boolean {
  return modelId in FIREWORKS_MODEL_METADATA;
}

/**
 * Get all available Fireworks models with metadata
 */
export function getFireworksModelsWithMetadata(): FireworksModelMetadata[] {
  return Object.values(FIREWORKS_MODEL_METADATA);
}

/**
 * Get model provider for grouping
 */
export function getFireworksModelProvider(modelId: string): string {
  const metadata = getFireworksModelMetadata(modelId);
  return metadata?.provider || 'Unknown';
}
