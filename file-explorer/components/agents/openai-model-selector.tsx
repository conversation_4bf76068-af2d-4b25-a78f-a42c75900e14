// components/agents/openai-model-selector.tsx
import React, { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { getOpenAIModelMetadata, getOpenAIModelSeries, hasOpenAIModelMetadata } from './openai-models';
import { ModelRegistryService } from './model-registry-service';

interface OpenAIModelSelectorProps {
  value: string;
  onChange: (value: string) => void;
  apiKey: string;
  availableModels: string[];
  isLoadingModels: boolean;
  onRefreshModels: () => void;
  showCustomInput?: boolean;
  disabled?: boolean;
  placeholder?: string;
}

export const OpenAIModelSelector: React.FC<OpenAIModelSelectorProps> = ({
  value,
  onChange,
  apiKey,
  availableModels,
  isLoadingModels,
  onRefreshModels,
  showCustomInput = true,
  disabled = false,
  placeholder = "Select an OpenAI model"
}) => {
  const [showCustom, setShowCustom] = useState(false);
  const [customModel, setCustomModel] = useState('');

  const selectedModelMetadata = getOpenAIModelMetadata(value);

  const handleSelectChange = (selectedValue: string) => {
    if (selectedValue === 'custom') {
      setShowCustom(true);
    } else {
      setShowCustom(false);
      onChange(selectedValue);
    }
  };

  const handleCustomSubmit = () => {
    if (customModel.trim()) {
      onChange(customModel.trim());
      setShowCustom(false);
      setCustomModel('');
    }
  };

  const handleCustomCancel = () => {
    setShowCustom(false);
    setCustomModel('');
  };

  const getModelBadgeVariant = (modelId: string) => {
    if (modelId.startsWith('gpt-4o')) {
      return 'default'; // GPT-4o series
    }
    if (modelId.startsWith('gpt-4')) {
      return 'secondary'; // GPT-4 series
    }
    if (modelId.startsWith('gpt-3.5')) {
      return 'outline'; // GPT-3.5 series
    }
    return 'secondary'; // Other models
  };

  // Group models by series for better organization
  const groupedModels = {
    'gpt-4o': availableModels.filter(m => m.startsWith('gpt-4o')),
    'gpt-4': availableModels.filter(m => m.startsWith('gpt-4') && !m.startsWith('gpt-4o')),
    'gpt-3.5': availableModels.filter(m => m.startsWith('gpt-3.5')),
    'other': availableModels.filter(m => !m.startsWith('gpt-4') && !m.startsWith('gpt-3.5'))
  };

  const isElectronAPIAvailable = () => {
    return typeof window !== 'undefined' && window.electronAPI?.llm?.fetchModels !== undefined;
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <Label>Model</Label>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onRefreshModels}
            disabled={isLoadingModels || !apiKey || !isElectronAPIAvailable()}
            className="h-6 px-2 text-xs"
          >
            {isLoadingModels ? (
              <div className="animate-spin h-3 w-3 border border-gray-300 border-t-blue-600 rounded-full" />
            ) : (
              'Refresh'
            )}
          </Button>
          {!isElectronAPIAvailable() && (
            <span className="text-xs text-orange-600 dark:text-orange-400">
              Electron required
            </span>
          )}
        </div>
      </div>

      {/* Custom Model Input */}
      {showCustom ? (
        <div className="space-y-2">
          <Input
            value={customModel}
            onChange={(e) => setCustomModel(e.target.value)}
            placeholder="Enter custom OpenAI model ID"
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleCustomSubmit();
              } else if (e.key === 'Escape') {
                handleCustomCancel();
              }
            }}
            autoFocus
          />
          <div className="flex gap-2">
            <Button size="sm" onClick={handleCustomSubmit} disabled={!customModel.trim()}>
              Use Model
            </Button>
            <Button size="sm" variant="outline" onClick={handleCustomCancel}>
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        /* Model Selection Dropdown */
        <Select
          value={value}
          onValueChange={handleSelectChange}
          disabled={disabled || isLoadingModels}
        >
          <SelectTrigger>
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {/* GPT-4o Series */}
            {groupedModels['gpt-4o'].length > 0 && (
              <>
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b">
                  GPT-4o Series (Latest)
                </div>
                {groupedModels['gpt-4o'].map((modelId) => (
                  <SelectItem key={modelId} value={modelId}>
                    <div className="flex items-center gap-2">
                      <span>{getOpenAIModelMetadata(modelId)?.label || modelId}</span>
                      {hasOpenAIModelMetadata(modelId) && (
                        <Badge variant="default" className="text-xs">Verified</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </>
            )}

            {/* GPT-4 Series */}
            {groupedModels['gpt-4'].length > 0 && (
              <>
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b mt-2">
                  GPT-4 Series
                </div>
                {groupedModels['gpt-4'].map((modelId) => (
                  <SelectItem key={modelId} value={modelId}>
                    <div className="flex items-center gap-2">
                      <span>{getOpenAIModelMetadata(modelId)?.label || modelId}</span>
                      {hasOpenAIModelMetadata(modelId) && (
                        <Badge variant="secondary" className="text-xs">Verified</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </>
            )}

            {/* GPT-3.5 Series */}
            {groupedModels['gpt-3.5'].length > 0 && (
              <>
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b mt-2">
                  GPT-3.5 Series
                </div>
                {groupedModels['gpt-3.5'].map((modelId) => (
                  <SelectItem key={modelId} value={modelId}>
                    <div className="flex items-center gap-2">
                      <span>{getOpenAIModelMetadata(modelId)?.label || modelId}</span>
                      {hasOpenAIModelMetadata(modelId) && (
                        <Badge variant="outline" className="text-xs">Verified</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </>
            )}

            {/* Other Models */}
            {groupedModels['other'].length > 0 && (
              <>
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b mt-2">
                  Other Models
                </div>
                {groupedModels['other'].map((modelId) => (
                  <SelectItem key={modelId} value={modelId}>
                    {modelId}
                  </SelectItem>
                ))}
              </>
            )}

            {/* Custom Model Option */}
            {showCustomInput && (
              <>
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b mt-2">
                  Custom Model
                </div>
                <SelectItem value="custom">
                  <div className="flex items-center gap-2">
                    <span>Custom Model ID...</span>
                    <Badge variant="outline" className="text-xs">Manual</Badge>
                  </div>
                </SelectItem>
              </>
            )}
          </SelectContent>
        </Select>
      )}

      {/* Model Metadata Display */}
      {selectedModelMetadata && !showCustom && (
        <div className="p-3 bg-muted rounded-lg space-y-2">
          <div className="flex items-center gap-2">
            <Badge variant={getModelBadgeVariant(selectedModelMetadata.id)}>
              {getOpenAIModelSeries(selectedModelMetadata.id)}
            </Badge>
            <span className="text-sm font-medium">{selectedModelMetadata.label}</span>
          </div>
          
          {selectedModelMetadata.description && (
            <p className="text-sm text-muted-foreground">
              {selectedModelMetadata.description}
            </p>
          )}
          
          <div className="grid grid-cols-2 gap-4 text-xs">
            {selectedModelMetadata.contextSize && (
              <div>
                <span className="font-medium">Context:</span> {selectedModelMetadata.contextSize.toLocaleString()} tokens
              </div>
            )}
            {selectedModelMetadata.pricing && (
              <div>
                <span className="font-medium">Cost:</span> ${selectedModelMetadata.pricing.input}/${selectedModelMetadata.pricing.output} per 1K tokens
              </div>
            )}
          </div>
          
          {selectedModelMetadata.tags && selectedModelMetadata.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {selectedModelMetadata.tags.map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Custom Model Info */}
      {!selectedModelMetadata && value && !showCustom && (
        <div className="p-3 bg-muted rounded-lg">
          <div className="flex items-center gap-2">
            <Badge variant="outline">Custom</Badge>
            <span className="text-sm font-medium">{value}</span>
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            Custom OpenAI model ID
          </p>
        </div>
      )}
    </div>
  );
};

export default OpenAIModelSelector;
