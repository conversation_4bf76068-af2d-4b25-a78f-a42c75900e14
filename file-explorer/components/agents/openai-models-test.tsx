// components/agents/openai-models-test.tsx
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { OpenAIModelSelector } from './openai-model-selector';
import { 
  OPENAI_MODEL_METADATA, 
  getOpenAIModelMetadata, 
  hasOpenAIModelMetadata,
  getOpenAIModelsWithMetadata,
  getOpenAIModelSeries
} from './openai-models';

export const OpenAIModelsTest: React.FC = () => {
  const [selectedModel, setSelectedModel] = useState('gpt-4o');
  const [apiKey, setApiKey] = useState('');
  const [availableModels, setAvailableModels] = useState([
    'gpt-4o', 'gpt-4o-mini', 'gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo',
    'gpt-4-vision-preview', 'gpt-4-1106-preview', 'gpt-3.5-turbo-16k'
  ]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);

  const handleRefreshModels = () => {
    setIsLoadingModels(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoadingModels(false);
      console.log('Models refreshed');
    }, 1000);
  };

  const selectedModelData = getOpenAIModelMetadata(selectedModel);
  const modelsWithMetadata = getOpenAIModelsWithMetadata();

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>OpenAI Models Test Suite</CardTitle>
        <CardDescription>
          Test the OpenAI models metadata and selector component
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Model Selector Test */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Model Selector Test</h3>
          <OpenAIModelSelector
            value={selectedModel}
            onChange={setSelectedModel}
            apiKey={apiKey}
            availableModels={availableModels}
            isLoadingModels={isLoadingModels}
            onRefreshModels={handleRefreshModels}
            placeholder="Select an OpenAI model"
          />
        </div>

        {/* Selected Model Info */}
        {selectedModelData && (
          <div className="space-y-2">
            <h4 className="font-semibold">Selected Model Details</h4>
            <div className="p-4 bg-muted rounded-lg space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant="default">{getOpenAIModelSeries(selectedModelData.id)}</Badge>
                <span className="font-medium">{selectedModelData.label}</span>
              </div>
              <p className="text-sm text-muted-foreground">{selectedModelData.description}</p>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Context Size:</span> {selectedModelData.contextSize?.toLocaleString()} tokens
                </div>
                <div>
                  <span className="font-medium">Pricing:</span> ${selectedModelData.pricing?.input}/${selectedModelData.pricing?.output} per 1K tokens
                </div>
              </div>
              {selectedModelData.tags && (
                <div className="flex flex-wrap gap-1">
                  {selectedModelData.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Metadata Coverage */}
        <div className="space-y-2">
          <h4 className="font-semibold">Metadata Coverage</h4>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <span className="text-sm font-medium">Available Models:</span> {availableModels.length}
            </div>
            <div>
              <span className="text-sm font-medium">With Metadata:</span> {availableModels.filter(hasOpenAIModelMetadata).length}
            </div>
          </div>
          <div className="space-y-1">
            {availableModels.map((model) => (
              <div key={model} className="flex items-center justify-between text-sm">
                <span>{model}</span>
                <Badge variant={hasOpenAIModelMetadata(model) ? "default" : "outline"}>
                  {hasOpenAIModelMetadata(model) ? "Verified" : "Dynamic"}
                </Badge>
              </div>
            ))}
          </div>
        </div>

        {/* All Models with Metadata */}
        <div className="space-y-2">
          <h4 className="font-semibold">All Models with Verified Metadata</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-48 overflow-y-auto">
            {modelsWithMetadata.map((model) => (
              <div key={model.id} className="p-2 bg-muted rounded text-sm">
                <div className="font-medium">{model.label}</div>
                <div className="text-xs text-muted-foreground">{model.id}</div>
                <div className="text-xs">
                  {model.contextSize?.toLocaleString()} tokens • ${model.pricing?.input}/${model.pricing?.output}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Test Controls */}
        <div className="space-y-2">
          <h4 className="font-semibold">Test Controls</h4>
          <div className="flex gap-2">
            <Button 
              onClick={() => setSelectedModel('gpt-4o')}
              variant="outline"
              size="sm"
            >
              Select GPT-4o
            </Button>
            <Button 
              onClick={() => setSelectedModel('gpt-4')}
              variant="outline"
              size="sm"
            >
              Select GPT-4
            </Button>
            <Button 
              onClick={() => setSelectedModel('custom-model-123')}
              variant="outline"
              size="sm"
            >
              Select Custom Model
            </Button>
            <Button 
              onClick={() => setAvailableModels([...availableModels, 'gpt-5-preview'])}
              variant="outline"
              size="sm"
            >
              Add Fake Model
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default OpenAIModelsTest;
