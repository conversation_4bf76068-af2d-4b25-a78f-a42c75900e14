// components/settings/settings-ui.tsx
import React, { useState, useEffect } from 'react';
import { SettingsManager, AllSettings, AgentSettings } from './settings-manager';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Save, Download, Upload, RotateCcw, Eye, EyeOff } from 'lucide-react';
import { ApiKeysSettings } from './api-keys-settings';
import { getAllProviders, getProviderModels } from '../agents/llm-provider-registry';

interface SettingsUIProps {
  settingsManager: SettingsManager;
  onClose: () => void;
}

export const SettingsUI: React.FC<SettingsUIProps> = ({ settingsManager, onClose }) => {
  const [settings, setSettings] = useState<AllSettings>(settingsManager.getSettings());
  const [showApiKeys, setShowApiKeys] = useState(false);
  const [activeTab, setActiveTab] = useState('system');

  useEffect(() => {
    const handleSettingsChange = (newSettings: AllSettings) => {
      setSettings(newSettings);
    };

    settingsManager.onSettingsChange(handleSettingsChange);
    return () => settingsManager.offSettingsChange(handleSettingsChange);
  }, [settingsManager]);

  const handleSystemSettingChange = (key: keyof typeof settings.system, value: any) => {
    settingsManager.updateSystemSettings({ [key]: value });
  };

  const handleAgentSettingChange = (agentId: string, key: keyof AgentSettings, value: any) => {
    settingsManager.updateAgentSettings(agentId, { [key]: value });
  };

  const handleApiKeyChange = (provider: string, key: string) => {
    if (key.trim()) {
      settingsManager.setApiKey(provider, key);
    } else {
      settingsManager.removeApiKey(provider);
    }
  };

  const exportSettings = () => {
    const data = settingsManager.exportSettings();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'synapse-settings.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        if (settingsManager.importSettings(content)) {
          alert('Settings imported successfully!');
        } else {
          alert('Failed to import settings. Please check the file format.');
        }
      };
      reader.readAsText(file);
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="border-b border-border p-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Settings</h2>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={exportSettings}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <label htmlFor="import-settings">
              <Button variant="outline" size="sm" as="span">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <input
                id="import-settings"
                type="file"
                accept=".json"
                className="hidden"
                onChange={importSettings}
              />
            </label>
            <Button variant="outline" size="sm" onClick={() => settingsManager.resetToDefaults()}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button onClick={onClose}>
              <Save className="h-4 w-4 mr-2" />
              Close
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-auto">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="system">System</TabsTrigger>
            <TabsTrigger value="agents">Agents</TabsTrigger>
            <TabsTrigger value="api-keys">API Keys</TabsTrigger>
            <TabsTrigger value="cost">Cost</TabsTrigger>
            <TabsTrigger value="privacy">Privacy</TabsTrigger>
            <TabsTrigger value="editor">Editor</TabsTrigger>
          </TabsList>

          <TabsContent value="system" className="space-y-6 p-6">
            <Card>
              <CardHeader>
                <CardTitle>System Settings</CardTitle>
                <CardDescription>Configure global application behavior</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-save">Auto Save</Label>
                  <Switch
                    id="auto-save"
                    checked={settings.system.autoSave}
                    onCheckedChange={(checked) => handleSystemSettingChange('autoSave', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Auto Save Interval (seconds)</Label>
                  <Slider
                    value={[settings.system.autoSaveInterval]}
                    onValueChange={([value]) => handleSystemSettingChange('autoSaveInterval', value)}
                    min={10}
                    max={300}
                    step={10}
                  />
                  <div className="text-sm text-muted-foreground">{settings.system.autoSaveInterval} seconds</div>
                </div>

                <div className="space-y-2">
                  <Label>Max Concurrent Tasks</Label>
                  <Slider
                    value={[settings.system.maxConcurrentTasks]}
                    onValueChange={([value]) => handleSystemSettingChange('maxConcurrentTasks', value)}
                    min={1}
                    max={20}
                    step={1}
                  />
                  <div className="text-sm text-muted-foreground">{settings.system.maxConcurrentTasks} tasks</div>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="debug-mode">Debug Mode</Label>
                  <Switch
                    id="debug-mode"
                    checked={settings.system.debugMode}
                    onCheckedChange={(checked) => handleSystemSettingChange('debugMode', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="agents" className="space-y-6 p-6">
            <div className="grid gap-4">
              {settings.agents.map((agent) => (
                <Card key={agent.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        {agent.name}
                        <Badge variant={agent.enabled ? 'default' : 'secondary'}>
                          {agent.enabled ? 'Enabled' : 'Disabled'}
                        </Badge>
                      </CardTitle>
                      <Switch
                        checked={agent.enabled}
                        onCheckedChange={(checked) => handleAgentSettingChange(agent.id, 'enabled', checked)}
                      />
                    </div>
                    <CardDescription>
                      Capabilities: {agent.capabilities.join(', ')}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label>Provider</Label>
                        <Select
                          value={agent.provider}
                          onValueChange={(value) => handleAgentSettingChange(agent.id, 'provider', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {getAllProviders().map((provider) => (
                              <SelectItem key={provider} value={provider}>
                                {provider.charAt(0).toUpperCase() + provider.slice(1)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Model</Label>
                        <Select
                          value={agent.model}
                          onValueChange={(value) => handleAgentSettingChange(agent.id, 'model', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {agent.provider && getProviderModels(agent.provider).map((model) => (
                              <SelectItem key={model} value={model}>
                                {model}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Max Tokens</Label>
                        <Input
                          type="number"
                          value={agent.maxTokens}
                          onChange={(e) => handleAgentSettingChange(agent.id, 'maxTokens', parseInt(e.target.value))}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Temperature</Label>
                      <Slider
                        value={[agent.temperature]}
                        onValueChange={([value]) => handleAgentSettingChange(agent.id, 'temperature', value)}
                        min={0}
                        max={1}
                        step={0.1}
                      />
                      <div className="text-sm text-muted-foreground">{agent.temperature}</div>
                    </div>

                    {agent.customPrompt !== undefined && (
                      <div className="space-y-2">
                        <Label>Custom Prompt</Label>
                        <Textarea
                          value={agent.customPrompt}
                          onChange={(e) => handleAgentSettingChange(agent.id, 'customPrompt', e.target.value)}
                          rows={3}
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="api-keys" className="space-y-6 p-6">
            <ApiKeysSettings settingsManager={settingsManager} settings={settings} />
          </TabsContent>

          <TabsContent value="cost" className="space-y-6 p-6">
            <Card>
              <CardHeader>
                <CardTitle>Cost Management</CardTitle>
                <CardDescription>Configure budget and cost tracking</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Monthly Budget Limit ($)</Label>
                  <Input
                    type="number"
                    value={settings.cost.budgetLimit}
                    onChange={(e) => settingsManager.updateCostSettings({ budgetLimit: parseFloat(e.target.value) })}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Alert Threshold (%)</Label>
                  <Slider
                    value={[settings.cost.alertThreshold]}
                    onValueChange={([value]) => settingsManager.updateCostSettings({ alertThreshold: value })}
                    min={10}
                    max={100}
                    step={5}
                  />
                  <div className="text-sm text-muted-foreground">{settings.cost.alertThreshold}%</div>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="track-usage">Track Usage</Label>
                  <Switch
                    id="track-usage"
                    checked={settings.cost.trackUsage}
                    onCheckedChange={(checked) => settingsManager.updateCostSettings({ trackUsage: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="show-estimates">Show Cost Estimates</Label>
                  <Switch
                    id="show-estimates"
                    checked={settings.cost.showCostEstimates}
                    onCheckedChange={(checked) => settingsManager.updateCostSettings({ showCostEstimates: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="prefer-cheaper">Prefer Cheaper Models</Label>
                  <Switch
                    id="prefer-cheaper"
                    checked={settings.cost.preferCheaperModels}
                    onCheckedChange={(checked) => settingsManager.updateCostSettings({ preferCheaperModels: checked })}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="privacy" className="space-y-6 p-6">
            <Card>
              <CardHeader>
                <CardTitle>Privacy Settings</CardTitle>
                <CardDescription>Control data sharing and privacy</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="local-only">Local Only Mode</Label>
                  <Switch
                    id="local-only"
                    checked={settings.privacy.localOnly}
                    onCheckedChange={(checked) => settingsManager.updatePrivacySettings({ localOnly: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="encrypt-prompts">Encrypt Prompts</Label>
                  <Switch
                    id="encrypt-prompts"
                    checked={settings.privacy.encryptPrompts}
                    onCheckedChange={(checked) => settingsManager.updatePrivacySettings({ encryptPrompts: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="clear-history">Clear History on Exit</Label>
                  <Switch
                    id="clear-history"
                    checked={settings.privacy.clearHistoryOnExit}
                    onCheckedChange={(checked) => settingsManager.updatePrivacySettings({ clearHistoryOnExit: checked })}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Max History Days</Label>
                  <Slider
                    value={[settings.privacy.maxHistoryDays]}
                    onValueChange={([value]) => settingsManager.updatePrivacySettings({ maxHistoryDays: value })}
                    min={1}
                    max={365}
                    step={1}
                  />
                  <div className="text-sm text-muted-foreground">{settings.privacy.maxHistoryDays} days</div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="editor" className="space-y-6 p-6">
            <Card>
              <CardHeader>
                <CardTitle>Editor Settings</CardTitle>
                <CardDescription>Configure code editor preferences</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Font Size</Label>
                    <Slider
                      value={[settings.editor.fontSize]}
                      onValueChange={([value]) => settingsManager.updateEditorSettings({ fontSize: value })}
                      min={10}
                      max={24}
                      step={1}
                    />
                    <div className="text-sm text-muted-foreground">{settings.editor.fontSize}px</div>
                  </div>

                  <div className="space-y-2">
                    <Label>Tab Size</Label>
                    <Slider
                      value={[settings.editor.tabSize]}
                      onValueChange={([value]) => settingsManager.updateEditorSettings({ tabSize: value })}
                      min={2}
                      max={8}
                      step={1}
                    />
                    <div className="text-sm text-muted-foreground">{settings.editor.tabSize} spaces</div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Font Family</Label>
                  <Input
                    value={settings.editor.fontFamily}
                    onChange={(e) => settingsManager.updateEditorSettings({ fontFamily: e.target.value })}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="word-wrap">Word Wrap</Label>
                    <Switch
                      id="word-wrap"
                      checked={settings.editor.wordWrap}
                      onCheckedChange={(checked) => settingsManager.updateEditorSettings({ wordWrap: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="line-numbers">Line Numbers</Label>
                    <Switch
                      id="line-numbers"
                      checked={settings.editor.lineNumbers}
                      onCheckedChange={(checked) => settingsManager.updateEditorSettings({ lineNumbers: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="minimap">Minimap</Label>
                    <Switch
                      id="minimap"
                      checked={settings.editor.minimap}
                      onCheckedChange={(checked) => settingsManager.updateEditorSettings({ minimap: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="auto-format">Auto Format</Label>
                    <Switch
                      id="auto-format"
                      checked={settings.editor.autoFormat}
                      onCheckedChange={(checked) => settingsManager.updateEditorSettings({ autoFormat: checked })}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default SettingsUI;